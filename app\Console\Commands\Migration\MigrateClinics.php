<?php

namespace App\Console\Commands\Migration;

use App\Models\Clinic;
use App\Services\Migration\DataTransformer;
use Exception;
use Illuminate\Support\Facades\Log;

/**
 * Migrate Clinics Command
 * 
 * Simple focused migration for clinics only
 */
class MigrateClinics extends BaseMigrationCommand
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'migratewp:clinics
                            {--dry-run : Preview what would be migrated without executing}
                            {--force : Skip confirmation prompts}';

    /**
     * The console command description.
     */
    protected $description = 'Migrate clinics from WordPress to Laravel';

    /**
     * Data transformer instance
     */
    protected $transformer;

    /**
     * Log channels
     */
    protected $logChannel;
    protected $errorLogChannel;

    public function __construct()
    {
        parent::__construct();
        $this->transformer = new DataTransformer();
        $this->setupLogging();
    }

    /**
     * Setup dedicated logging for clinics migration
     */
    protected function setupLogging()
    {
        $date = now()->format('Y_m_d');
        $logPath = storage_path("logs/migration/clinics");
        
        // Create directory if it doesn't exist
        if (!file_exists($logPath)) {
            mkdir($logPath, 0755, true);
        }

        // Configure log channels
        config([
            'logging.channels.clinics_migration' => [
                'driver' => 'single',
                'path' => $logPath . "/clinics_{$date}.log",
                'level' => 'info',
            ],
            'logging.channels.clinics_errors' => [
                'driver' => 'single',
                'path' => $logPath . "/clinics_errors_{$date}.log",
                'level' => 'error',
            ]
        ]);

        $this->logChannel = 'clinics_migration';
        $this->errorLogChannel = 'clinics_errors';
    }

    /**
     * Execute the command
     */
    protected function executeCommand()
    {
        // Check if user wants to fix existing clinic IDs
        if ($this->option('fix-ids')) {
            return $this->fixExistingClinicIds();
        }
        $this->info("=== MIGRATING CLINICS ===");
        $this->logInfo("Starting clinics migration");
        
        if ($this->isDryRun()) {
            $this->warn("DRY RUN MODE - Previewing migration without making changes");
            $this->logInfo("Running in DRY RUN mode");
        }

        try {
            // 1. Make API call - get all clinics
            $this->info("Fetching clinics from WordPress...");
            $this->logInfo("Making API request to get clinics");
            
            $response = $this->makeApiRequest('laravel_get_clinics');
            $wpClinics = $response['data'] ?? [];

            if (empty($wpClinics)) {
                $this->info("No clinics found to migrate");
                $this->logInfo("No clinics found in WordPress");
                return 0;
            }

            $this->info("Found " . count($wpClinics) . " clinics to migrate");
            $this->logInfo("Found " . count($wpClinics) . " clinics to migrate");

            // 2. Process each clinic
            $processed = 0;
            $skipped = 0;
            $errors = 0;

            foreach ($wpClinics as $wpClinic) {
                try {
                    $clinicName = $wpClinic['name'] ?? 'Unknown';
                    $clinicEmail = $wpClinic['email'] ?? 'No email';
                    
                    if ($this->isDryRun()) {
                        $this->info("Would migrate clinic: {$clinicName} (Email: {$clinicEmail})");
                        $this->logInfo("DRY RUN: Would migrate clinic ID {$wpClinic['id']}: {$clinicName}");
                        $processed++;
                        continue;
                    }

                    // Skip if email already exists (your email-based approach)
                    if ($clinicEmail !== 'No email' && $this->emailExists($clinicEmail)) {
                        $this->info("Skipped clinic: {$clinicName} (Email already exists: {$clinicEmail})");
                        $this->logInfo("Skipped clinic ID {$wpClinic['id']}: {$clinicName} - Email already exists");
                        $skipped++;
                        continue;
                    }

                    // Transform WordPress clinic data to Laravel format
                    $laravelData = $this->transformer->transformClinic($wpClinic);
                    $laravelData['wp_clinic_id'] = $wpClinic['id'];

                    // First try to find by wp_clinic_id, then by email (for existing clinics)
                    $clinic = Clinic::where('wp_clinic_id', $wpClinic['id'])->first();

                    if (!$clinic) {
                        // If not found by wp_clinic_id, try by email (for existing clinics without wp_clinic_id)
                        $clinic = Clinic::where('email', $wpClinic['email'])->first();
                    }

                    if ($clinic) {
                        // Update existing clinic with wp_clinic_id and other data
                        $clinic->update($laravelData);
                        $this->info("✓ Updated existing clinic: {$clinic->name} (Laravel ID: {$clinic->id}) with WP ID: {$wpClinic['id']}");
                    } else {
                        // Create new clinic
                        $clinic = Clinic::create($laravelData);
                        $this->info("✓ Created new clinic: {$clinic->name} (Laravel ID: {$clinic->id}) with WP ID: {$wpClinic['id']}");
                    }

                    $this->info("✓ Migrated clinic: {$clinic->name} (Laravel ID: {$clinic->id})");
                    $this->logInfo("Successfully migrated clinic ID {$wpClinic['id']} → Laravel ID {$clinic->id}: {$clinic->name}");
                    $processed++;

                } catch (Exception $e) {
                    $this->error("✗ Failed to migrate clinic ID {$wpClinic['id']}: " . $e->getMessage());
                    $this->logError("Failed to migrate clinic ID {$wpClinic['id']}: " . $e->getMessage(), $wpClinic);
                    $errors++;
                }
            }

            // 3. Generate summary
            $this->generateSummary($processed, $skipped, $errors, count($wpClinics));

            return 0;

        } catch (Exception $e) {
            $this->error("Clinics migration failed: " . $e->getMessage());
            $this->logError("Clinics migration failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * Check if email already exists in system
     */
    protected function emailExists($email)
    {
        return \App\Models\User::where('email', $email)->exists();
    }

    /**
     * Generate migration summary
     */
    protected function generateSummary($processed, $skipped, $errors, $total)
    {
        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 CLINICS MIGRATION SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("Total Found: {$total}");
        $this->info("✅ Processed: {$processed}");
        $this->info("⏭️  Skipped: {$skipped}");
        $this->info("❌ Errors: {$errors}");
        
        if ($errors > 0) {
            $this->error("\n⚠️  Some clinics failed to migrate. Check error logs for details.");
        } else {
            $this->info("\n🎉 All clinics migrated successfully!");
        }
        
        $this->info("📁 Logs saved to: storage/logs/migration/clinics/");
        $this->info(str_repeat("=", 60));

        // Log summary
        $this->logInfo("Migration completed - Processed: {$processed}, Skipped: {$skipped}, Errors: {$errors}");
    }

    /**
     * Log info message
     */
    protected function logInfo($message, $context = [])
    {
        Log::channel($this->logChannel)->info($message, $context);
    }

    /**
     * Log error message
     */
    protected function logError($message, $context = [])
    {
        Log::channel($this->errorLogChannel)->error($message, $context);
    }

    /**
     * Fix wp_clinic_id values for existing clinics based on migration logs
     */
    protected function fixExistingClinicIds()
    {
        $this->info("=== FIXING CLINIC wp_clinic_id VALUES ===");
        $this->logInfo("Starting clinic ID fix");

        // Based on the successful migration logs from 2025-07-21 17:05:05
        $mapping = [
            // WordPress ID => Clinic Name (to find by name)
            40 => 'Dr Cuong Nguyen',
            27 => 'Mirza',
            26 => 'edgbaston wellness clinic',
            22 => 'Rainbow Diagnostics Ltd',
            16 => 'Demo Clinic1',
            15 => 'CHC Urgent care',
            13 => 'Doctor Chechi',
            12 => 'Dr Asiyahs Clinic',
            11 => 'Dr. Tong Practice',
            10 => 'Dr. Ihsan Clinic',
            9 => 'KSP Health UK',
            8 => 'MIO Health',
            6 => 'Prof Bolton Practice',
            5 => 'Aespect Clinic',
            4 => 'Govender Test Clinic',
            1 => 'Rainbow Labs',
        ];

        $updated = 0;
        $errors = 0;

        foreach ($mapping as $wpId => $clinicName) {
            try {
                $clinic = Clinic::where('name', $clinicName)->first();
                if ($clinic) {
                    $clinic->update(['wp_clinic_id' => $wpId]);
                    $this->info("✅ Updated: {$clinicName} → WP ID {$wpId} (Laravel ID: {$clinic->id})");
                    $this->logInfo("Fixed clinic ID: {$clinicName} → WP ID {$wpId}");
                    $updated++;
                } else {
                    $this->error("❌ Clinic not found: {$clinicName}");
                    $this->logError("Clinic not found: {$clinicName}");
                    $errors++;
                }
            } catch (Exception $e) {
                $this->error("❌ Error updating {$clinicName}: " . $e->getMessage());
                $this->logError("Error updating {$clinicName}: " . $e->getMessage());
                $errors++;
            }
        }

        $this->info("\n" . str_repeat("=", 60));
        $this->info("📊 CLINIC ID FIX SUMMARY");
        $this->info(str_repeat("=", 60));
        $this->info("✅ Updated: {$updated}");
        $this->info("❌ Errors: {$errors}");

        if ($errors > 0) {
            $this->error("\n⚠️  Some clinic IDs failed to update. Check error logs for details.");
        } else {
            $this->info("\n🎉 All clinic IDs fixed successfully!");
        }

        $this->logInfo("Clinic ID fix completed - Updated: {$updated}, Errors: {$errors}");

        return 0;
    }
}
